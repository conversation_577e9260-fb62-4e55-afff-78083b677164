#!/usr/bin/env python3
"""
Professional Directory Structure Organizer for AreTomo3 GUI
==========================================================

This script organizes the project structure according to professional standards
and creates a clean, distributable package.
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, List

class StructureOrganizer:
    """Organizes project structure professionally."""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent.absolute()
        self.dist_dir = self.root_dir / "dist"
        self.backup_dir = self.root_dir / "backup"
        
    def create_professional_structure(self):
        """Create professional directory structure."""
        print("🏗️  Creating professional directory structure...")
        
        # Define the target structure
        structure = {
            "aretomo3_gui/": "Main application package",
            "docs/": "Documentation and guides",
            "tests/": "Test suite and validation",
            "examples/": "Usage examples and tutorials",
            "scripts/": "Utility and build scripts",
            "resources/": "Static resources and assets",
            "config/": "Configuration templates",
            "data/": "Sample data and test files",
        }
        
        # Create directories if they don't exist
        for dir_name, description in structure.items():
            dir_path = self.root_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            print(f"✅ {dir_name:<20} - {description}")
        
        # Create __init__.py files where needed
        init_dirs = ["aretomo3_gui", "tests", "examples", "scripts"]
        for dir_name in init_dirs:
            init_file = self.root_dir / dir_name / "__init__.py"
            if not init_file.exists():
                init_file.touch()
    
    def organize_documentation(self):
        """Organize documentation files."""
        print("\n📚 Organizing documentation...")
        
        docs_dir = self.root_dir / "docs"
        
        # Ensure docs directory structure
        doc_structure = {
            "installation/": "Installation guides",
            "user_guide/": "User documentation",
            "developer/": "Developer documentation",
            "api/": "API reference",
            "examples/": "Documentation examples",
            "images/": "Documentation images"
        }
        
        for subdir, desc in doc_structure.items():
            (docs_dir / subdir).mkdir(exist_ok=True)
            print(f"✅ docs/{subdir:<15} - {desc}")
    
    def organize_tests(self):
        """Organize test files."""
        print("\n🧪 Organizing test structure...")
        
        tests_dir = self.root_dir / "tests"
        
        # Test directory structure
        test_structure = {
            "unit/": "Unit tests",
            "integration/": "Integration tests", 
            "functional/": "Functional tests",
            "performance/": "Performance tests",
            "fixtures/": "Test fixtures and data",
            "reports/": "Test reports and coverage"
        }
        
        for subdir, desc in test_structure.items():
            (tests_dir / subdir).mkdir(exist_ok=True)
            print(f"✅ tests/{subdir:<15} - {desc}")
    
    def create_config_templates(self):
        """Create configuration templates."""
        print("\n⚙️  Creating configuration templates...")
        
        config_dir = self.root_dir / "config"
        config_dir.mkdir(exist_ok=True)
        
        # Default configuration template
        default_config = {
            "application": {
                "name": "AreTomo3 GUI",
                "version": "3.0.0",
                "debug": False,
                "log_level": "INFO"
            },
            "gui": {
                "theme": "dark",
                "window_size": [1200, 800],
                "auto_save": True
            },
            "processing": {
                "max_threads": 4,
                "temp_directory": "/tmp/aretomo3",
                "output_format": "mrc"
            },
            "paths": {
                "aretomo3_executable": "",
                "default_input_dir": "",
                "default_output_dir": ""
            }
        }
        
        config_file = config_dir / "default_config.json"
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        print(f"✅ Created {config_file}")
    
    def create_resource_structure(self):
        """Create resources directory structure."""
        print("\n🎨 Creating resources structure...")
        
        resources_dir = self.root_dir / "resources"
        
        resource_structure = {
            "icons/": "Application icons",
            "themes/": "UI themes and styles",
            "templates/": "File templates",
            "fonts/": "Custom fonts",
            "sounds/": "Audio notifications"
        }
        
        for subdir, desc in resource_structure.items():
            (resources_dir / subdir).mkdir(exist_ok=True)
            print(f"✅ resources/{subdir:<12} - {desc}")
    
    def clean_unnecessary_files(self):
        """Clean up unnecessary files."""
        print("\n🧹 Cleaning unnecessary files...")
        
        # Files and directories to remove
        cleanup_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo", 
            "*.pyd",
            ".pytest_cache",
            ".coverage",
            "*.egg-info",
            ".DS_Store",
            "Thumbs.db",
            "*.tmp",
            "*.log"
        ]
        
        removed_count = 0
        for pattern in cleanup_patterns:
            if pattern.startswith("*."):
                # Handle file patterns
                for file_path in self.root_dir.rglob(pattern):
                    if file_path.is_file():
                        file_path.unlink()
                        removed_count += 1
            else:
                # Handle directory patterns
                for dir_path in self.root_dir.rglob(pattern):
                    if dir_path.is_dir():
                        shutil.rmtree(dir_path)
                        removed_count += 1
        
        print(f"✅ Removed {removed_count} unnecessary files/directories")
    
    def create_manifest_files(self):
        """Create manifest and metadata files."""
        print("\n📋 Creating manifest files...")
        
        # MANIFEST.in for package distribution
        manifest_content = """
include README.md
include LICENSE
include CHANGELOG.md
include requirements.txt
include pyproject.toml
recursive-include aretomo3_gui *.py
recursive-include docs *.md *.rst *.txt
recursive-include examples *.py *.md
recursive-include config *.json *.yaml *.toml
recursive-include resources *
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.so
recursive-exclude * .DS_Store
"""
        
        manifest_file = self.root_dir / "MANIFEST.in"
        with open(manifest_file, 'w') as f:
            f.write(manifest_content.strip())
        
        print(f"✅ Created {manifest_file}")
        
        # .gitignore
        gitignore_content = """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Logs
*.log

# Temporary files
*.tmp
*.temp
"""
        
        gitignore_file = self.root_dir / ".gitignore"
        with open(gitignore_file, 'w') as f:
            f.write(gitignore_content.strip())
        
        print(f"✅ Created {gitignore_file}")
    
    def generate_structure_report(self):
        """Generate a structure report."""
        print("\n📊 Generating structure report...")
        
        def get_directory_tree(path: Path, prefix: str = "", max_depth: int = 3, current_depth: int = 0) -> List[str]:
            """Generate directory tree representation."""
            if current_depth >= max_depth:
                return []
            
            items = []
            try:
                entries = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
                for i, entry in enumerate(entries):
                    if entry.name.startswith('.'):
                        continue
                    
                    is_last = i == len(entries) - 1
                    current_prefix = "└── " if is_last else "├── "
                    items.append(f"{prefix}{current_prefix}{entry.name}")
                    
                    if entry.is_dir() and current_depth < max_depth - 1:
                        extension_prefix = "    " if is_last else "│   "
                        items.extend(get_directory_tree(
                            entry, prefix + extension_prefix, max_depth, current_depth + 1
                        ))
            except PermissionError:
                pass
            
            return items
        
        tree_lines = [f"{self.root_dir.name}/"] + get_directory_tree(self.root_dir)
        
        report_content = f"""
# AreTomo3 GUI Project Structure Report

Generated on: {Path(__file__).stat().st_mtime}

## Directory Tree

```
{chr(10).join(tree_lines)}
```

## Structure Description

- **aretomo3_gui/**: Main application package containing all source code
- **docs/**: Comprehensive documentation including installation, user guides, and API reference
- **tests/**: Complete test suite with unit, integration, and performance tests
- **examples/**: Usage examples and tutorials for users and developers
- **scripts/**: Utility scripts for building, deployment, and maintenance
- **resources/**: Static resources including icons, themes, and templates
- **config/**: Configuration templates and default settings
- **data/**: Sample data files for testing and examples

## Key Files

- **README.md**: Main project documentation and quick start guide
- **install.py**: Professional installation script with dependency management
- **pyproject.toml**: Modern Python project configuration
- **requirements.txt**: Python dependencies specification
- **LICENSE**: Project license information
"""
        
        report_file = self.root_dir / "STRUCTURE_REPORT.md"
        with open(report_file, 'w') as f:
            f.write(report_content.strip())
        
        print(f"✅ Created {report_file}")

def main():
    """Main organization function."""
    print("🏗️  AreTomo3 GUI Professional Structure Organization")
    print("=" * 60)
    
    organizer = StructureOrganizer()
    
    # Execute organization steps
    steps = [
        organizer.create_professional_structure,
        organizer.organize_documentation,
        organizer.organize_tests,
        organizer.create_config_templates,
        organizer.create_resource_structure,
        organizer.clean_unnecessary_files,
        organizer.create_manifest_files,
        organizer.generate_structure_report
    ]
    
    for step in steps:
        try:
            step()
        except Exception as e:
            print(f"❌ Error in {step.__name__}: {e}")
            continue
    
    print("\n" + "=" * 60)
    print("✅ Professional structure organization completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()
