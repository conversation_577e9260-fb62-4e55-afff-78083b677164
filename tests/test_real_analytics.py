#!/usr/bin/env python3
"""
Real functional tests for advanced analytics - 100% coverage focused
Tests that exercise every code path in the analytics module.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path
from unittest.mock import patch, Mock

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestAdvancedAnalyticsRealCoverage:
    """Comprehensive tests that exercise every line of advanced_analytics.py"""
    
    def test_data_quality_analyzer_init(self):
        """Test DataQualityAnalyzer initialization - exercises constructor."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        assert analyzer.quality_thresholds is not None
        assert "resolution_excellent" in analyzer.quality_thresholds
        assert "drift_excellent" in analyzer.quality_thresholds
        assert analyzer.quality_thresholds["resolution_excellent"] == 3.0
    
    def test_motion_correction_analysis_all_branches(self):
        """Test analyze_motion_correction - exercises ALL code paths."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test successful analysis (exercises success path)
        motion_data = {
            "frame_shifts": [1.0, 2.0, 1.5, 0.8, 1.2],
            "total_drift": 5.3,
            "early_drift": 3.2,
            "late_drift": 1.1,
            "dataset_id": "test_dataset"
        }
        
        result = analyzer.analyze_motion_correction(motion_data)
        assert result.analysis_type == "motion_correction"
        assert result.dataset_id == "test_dataset"
        assert "total_drift" in result.metrics
        assert "early_drift" in result.metrics
        assert "late_drift" in result.metrics
        assert "drift_stability" in result.metrics
        assert "max_frame_shift" in result.metrics
        assert "mean_frame_shift" in result.metrics
        assert result.quality_score > 0
        assert len(result.recommendations) > 0
        
        # Test with empty frame_shifts (exercises empty list handling)
        motion_data_empty = {
            "frame_shifts": [],
            "total_drift": 5.3,
            "early_drift": 3.2,
            "late_drift": 1.1,
            "dataset_id": "test_empty"
        }
        
        result = analyzer.analyze_motion_correction(motion_data_empty)
        assert result.metrics["max_frame_shift"] == 0.0
        assert result.metrics["mean_frame_shift"] == 0.0
        
        # Test with zero late_drift (exercises division by zero handling)
        motion_data_zero = {
            "frame_shifts": [1.0, 2.0],
            "total_drift": 5.3,
            "early_drift": 3.2,
            "late_drift": 0.0,
            "dataset_id": "test_zero"
        }
        
        result = analyzer.analyze_motion_correction(motion_data_zero)
        assert result.metrics["drift_stability"] == 1.0
        
        # Test error handling (exercises except branch)
        invalid_data = {"invalid": "data"}
        result = analyzer.analyze_motion_correction(invalid_data)
        assert result.analysis_type == "motion_correction"
        assert result.dataset_id == "error"
        assert result.quality_score == 0.0
    
    def test_ctf_estimation_analysis_all_branches(self):
        """Test analyze_ctf_estimation - exercises ALL code paths."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test successful analysis (exercises success path)
        ctf_data = {
            "defocus_u": -2.5,
            "defocus_v": -2.8,
            "astigmatism": 150.0,
            "resolution": 3.5,
            "confidence": 0.85,
            "dataset_id": "test_ctf"
        }
        
        result = analyzer.analyze_ctf_estimation(ctf_data)
        assert result.analysis_type == "ctf_estimation"
        assert result.dataset_id == "test_ctf"
        assert "mean_defocus" in result.metrics
        assert "defocus_difference" in result.metrics
        assert result.metrics["mean_defocus"] == -2.65
        assert result.metrics["defocus_difference"] == 0.3
        assert result.quality_score > 0
        
        # Test error handling (exercises except branch)
        invalid_ctf_data = {"invalid": "ctf_data"}
        result = analyzer.analyze_ctf_estimation(invalid_ctf_data)
        assert result.analysis_type == "ctf_estimation"
        assert result.dataset_id == "error"
        assert result.quality_score == 0.0
    
    def test_motion_quality_assessment_all_thresholds(self):
        """Test _assess_motion_quality - exercises ALL threshold branches."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test excellent quality (exercises first branch)
        metrics_excellent = {"total_drift": 4.0}  # <= 5.0
        score = analyzer._assess_motion_quality(metrics_excellent)
        assert score == 1.0
        
        # Test good quality (exercises second branch)
        metrics_good = {"total_drift": 8.0}  # <= 10.0
        score = analyzer._assess_motion_quality(metrics_good)
        assert score == 0.8
        
        # Test fair quality (exercises third branch)
        metrics_fair = {"total_drift": 15.0}  # <= 20.0
        score = analyzer._assess_motion_quality(metrics_fair)
        assert score == 0.6
        
        # Test poor quality (exercises fourth branch)
        metrics_poor = {"total_drift": 25.0}  # > 20.0
        score = analyzer._assess_motion_quality(metrics_poor)
        assert score == 0.4
    
    def test_ctf_quality_assessment_all_thresholds(self):
        """Test _assess_ctf_quality - exercises ALL threshold branches."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test excellent resolution (exercises first branch)
        metrics_excellent = {
            "resolution": 2.5,  # <= 3.0
            "confidence": 0.9,
            "astigmatism": 80.0  # <= 100.0
        }
        score = analyzer._assess_ctf_quality(metrics_excellent)
        assert score > 0.9
        
        # Test good resolution (exercises second branch)
        metrics_good = {
            "resolution": 3.5,  # <= 4.0
            "confidence": 0.8,
            "astigmatism": 150.0  # <= 200.0
        }
        score = analyzer._assess_ctf_quality(metrics_good)
        assert 0.7 < score < 0.9
        
        # Test poor resolution (exercises third branch)
        metrics_poor = {
            "resolution": 5.0,  # > 4.0
            "confidence": 0.6,
            "astigmatism": 250.0  # > 200.0
        }
        score = analyzer._assess_ctf_quality(metrics_poor)
        assert score < 0.7
        
        # Test confidence capping (exercises min function)
        metrics_high_conf = {
            "resolution": 3.0,
            "confidence": 1.5,  # > 1.0, should be capped
            "astigmatism": 100.0
        }
        score = analyzer._assess_ctf_quality(metrics_high_conf)
        assert score <= 1.0
    
    def test_motion_recommendations_all_branches(self):
        """Test _generate_motion_recommendations - exercises ALL branches."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test high drift recommendation (exercises first branch)
        metrics_high_drift = {
            "total_drift": 15.0,  # > 10.0 (drift_good threshold)
            "early_drift": 5.0,
            "drift_stability": 1.5
        }
        recommendations = analyzer._generate_motion_recommendations(metrics_high_drift)
        assert any("High drift detected" in rec for rec in recommendations)
        
        # Test high early drift recommendation (exercises second branch)
        metrics_high_early = {
            "total_drift": 8.0,
            "early_drift": 6.0,  # > 8.0 * 0.7 = 5.6
            "drift_stability": 1.5
        }
        recommendations = analyzer._generate_motion_recommendations(metrics_high_early)
        assert any("High early drift" in rec for rec in recommendations)
        
        # Test unstable drift recommendation (exercises third branch)
        metrics_unstable = {
            "total_drift": 8.0,
            "early_drift": 3.0,
            "drift_stability": 2.5  # > 2.0
        }
        recommendations = analyzer._generate_motion_recommendations(metrics_unstable)
        assert any("Unstable drift pattern" in rec for rec in recommendations)
        
        # Test good quality (exercises fourth branch)
        metrics_good = {
            "total_drift": 5.0,  # <= 10.0
            "early_drift": 2.0,  # <= 5.0 * 0.7 = 3.5
            "drift_stability": 1.5  # <= 2.0
        }
        recommendations = analyzer._generate_motion_recommendations(metrics_good)
        assert any("quality is good" in rec for rec in recommendations)
    
    def test_ctf_recommendations_all_branches(self):
        """Test _generate_ctf_recommendations - exercises ALL branches."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test low resolution recommendation (exercises first branch)
        metrics_low_res = {
            "resolution": 5.0,  # > 4.0 (resolution_good threshold)
            "astigmatism": 150.0,
            "confidence": 0.8
        }
        recommendations = analyzer._generate_ctf_recommendations(metrics_low_res)
        assert any("Low resolution" in rec for rec in recommendations)
        
        # Test high astigmatism recommendation (exercises second branch)
        metrics_high_astig = {
            "resolution": 3.0,
            "astigmatism": 250.0,  # > 200.0 (astigmatism_good threshold)
            "confidence": 0.8
        }
        recommendations = analyzer._generate_ctf_recommendations(metrics_high_astig)
        assert any("High astigmatism" in rec for rec in recommendations)
        
        # Test low confidence recommendation (exercises third branch)
        metrics_low_conf = {
            "resolution": 3.0,
            "astigmatism": 150.0,
            "confidence": 0.6  # < 0.7
        }
        recommendations = analyzer._generate_ctf_recommendations(metrics_low_conf)
        assert any("Low CTF confidence" in rec for rec in recommendations)
        
        # Test good quality (exercises fourth branch)
        metrics_good = {
            "resolution": 3.0,  # <= 4.0
            "astigmatism": 150.0,  # <= 200.0
            "confidence": 0.8  # >= 0.7
        }
        recommendations = analyzer._generate_ctf_recommendations(metrics_good)
        assert any("quality is good" in rec for rec in recommendations)
    
    def test_statistical_analyzer_all_branches(self):
        """Test StatisticalAnalyzer - exercises ALL code paths."""
        from aretomo3_gui.analytics.advanced_analytics import StatisticalAnalyzer
        
        analyzer = StatisticalAnalyzer()
        assert analyzer.analysis_cache == {}
        
        # Test dataset trends with scipy available
        datasets = [
            {"timestamp": "2023-01-01", "resolution": 3.0, "drift": 5.0},
            {"timestamp": "2023-01-02", "resolution": 3.2, "drift": 4.8},
            {"timestamp": "2023-01-03", "resolution": 2.9, "drift": 5.2}
        ]
        
        result = analyzer.analyze_dataset_trends(datasets)
        if "error" not in result:  # If scipy is available
            assert "summary_statistics" in result
            assert "correlations" in result
            assert "trends" in result
            assert "dataset_count" in result
        
        # Test with empty datasets (exercises empty data branch)
        empty_result = analyzer.analyze_dataset_trends([])
        assert "error" in empty_result or "dataset_count" in empty_result
        
        # Test outlier detection with IQR method
        data = [1, 2, 3, 4, 5, 100]  # 100 is an outlier
        outlier_result = analyzer.perform_outlier_detection(data, method="iqr")
        assert "outliers" in outlier_result
        assert "outlier_indices" in outlier_result
        assert len(outlier_result["outliers"]) > 0
        assert 100 in outlier_result["outliers"]
        
        # Test outlier detection with zscore method
        outlier_result_z = analyzer.perform_outlier_detection(data, method="zscore")
        assert "outliers" in outlier_result_z
        
        # Test with empty data (exercises empty data branch)
        empty_outlier_result = analyzer.perform_outlier_detection([])
        assert empty_outlier_result["outliers"] == []
        assert empty_outlier_result["outlier_indices"] == []
    
    def test_advanced_analytics_main_class(self):
        """Test AdvancedAnalytics main class - exercises ALL methods."""
        from aretomo3_gui.analytics.advanced_analytics import AdvancedAnalytics
        
        analytics = AdvancedAnalytics()
        assert analytics.quality_analyzer is not None
        assert analytics.statistical_analyzer is not None
        assert analytics.ml_analyzer is not None
        assert analytics.analysis_history == []
        
        # Test motion correction analysis (exercises motion branch)
        motion_results = {
            "type": "motion_correction",
            "frame_shifts": [1.0, 2.0, 1.5],
            "total_drift": 4.5,
            "dataset_id": "test_motion"
        }
        
        result = analytics.analyze_processing_results(motion_results)
        assert result.analysis_type == "motion_correction"
        
        # Test CTF estimation analysis (exercises CTF branch)
        ctf_results = {
            "type": "ctf_estimation",
            "defocus_u": -2.5,
            "defocus_v": -2.8,
            "dataset_id": "test_ctf"
        }
        
        result = analytics.analyze_processing_results(ctf_results)
        assert result.analysis_type == "ctf_estimation"
        
        # Test unknown analysis type (exercises unknown branch)
        unknown_results = {
            "type": "unknown_type",
            "data": "test"
        }
        
        result = analytics.analyze_processing_results(unknown_results)
        assert result.analysis_type == "unknown_type"
        assert result.dataset_id == "error"
        
        # Test comprehensive report generation
        all_results = [motion_results, ctf_results]
        report = analytics.generate_comprehensive_report("test_dataset", all_results)
        assert "dataset_id" in report
        assert "overall_quality_score" in report
        assert "total_analyses" in report

    def test_machine_learning_analyzer_all_branches(self):
        """Test MachineLearningAnalyzer - exercises ALL code paths."""
        from aretomo3_gui.analytics.advanced_analytics import MachineLearningAnalyzer

        analyzer = MachineLearningAnalyzer()
        assert analyzer.models == {}
        assert analyzer.scalers == {}

        # Test clustering analysis with sufficient data
        test_data = [
            {"feature1": 1.0, "feature2": 2.0, "feature3": 3.0},
            {"feature1": 2.0, "feature2": 3.0, "feature3": 4.0},
            {"feature1": 10.0, "feature2": 11.0, "feature3": 12.0},
            {"feature1": 11.0, "feature2": 12.0, "feature3": 13.0}
        ]
        features = ["feature1", "feature2", "feature3"]

        result = analyzer.perform_clustering_analysis(test_data, features, n_clusters=2)
        if "error" not in result:  # If sklearn is available
            assert "cluster_labels" in result
            assert "cluster_centers" in result
            assert "cluster_analysis" in result

        # Test clustering with insufficient data (exercises error branch)
        small_data = [{"feature1": 1.0}]
        result_small = analyzer.perform_clustering_analysis(small_data, features, n_clusters=3)
        assert "error" in result_small

        # Test PCA analysis
        pca_result = analyzer.perform_pca_analysis(test_data, features)
        if "error" not in pca_result:  # If sklearn is available
            assert "explained_variance_ratio" in pca_result
            assert "components" in pca_result

        # Test PCA with insufficient data (exercises error branch)
        pca_result_small = analyzer.perform_pca_analysis([{"feature1": 1.0}], features)
        assert "error" in pca_result_small

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--cov=aretomo3_gui.analytics.advanced_analytics", "--cov-report=term-missing"])
