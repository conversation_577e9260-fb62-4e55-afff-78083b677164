#!/usr/bin/env python3
"""
Real functional tests for data management - 100% coverage focused
Tests that exercise every code path in the data management module.
"""

import pytest
import os
import json
import tempfile
from pathlib import Path
import sys
from unittest.mock import patch, Mock

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestDataManagerRealCoverage:
    """Comprehensive tests that exercise every line of data_manager.py"""
    
    def test_data_manager_init(self):
        """Test DataManager initialization - exercises constructor."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        assert manager.datasets == {}
        assert manager.current_dataset is None
        assert manager.data_cache == {}
        assert manager.metadata == {}
    
    def test_load_data_all_branches(self):
        """Test load_data - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test successful JSON loading (exercises success path)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            test_data = {
                "test_key": "test_value",
                "numeric_data": 123,
                "array_data": [1, 2, 3]
            }
            json.dump(test_data, tmp)
            tmp.flush()
            
            loaded_data = manager.load_data(tmp.name)
            assert loaded_data is not None
            assert loaded_data["test_key"] == "test_value"
            assert loaded_data["numeric_data"] == 123
            assert loaded_data["array_data"] == [1, 2, 3]
        
        os.unlink(tmp.name)
        
        # Test with non-existent file (exercises file not found branch)
        result = manager.load_data("/non/existent/file.json")
        assert result is None
        
        # Test with invalid JSON (exercises JSON decode error branch)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            tmp.write("invalid json content {")
            tmp.flush()
            
            result = manager.load_data(tmp.name)
            assert result is None
        
        os.unlink(tmp.name)
        
        # Test with permission error (exercises permission error branch)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            json.dump({"test": "data"}, tmp)
            tmp.flush()
            
            # Change permissions to make file unreadable
            os.chmod(tmp.name, 0o000)
            
            try:
                result = manager.load_data(tmp.name)
                assert result is None
            finally:
                # Restore permissions for cleanup
                os.chmod(tmp.name, 0o644)
        
        os.unlink(tmp.name)
    
    def test_save_data_all_branches(self):
        """Test save_data - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test successful saving (exercises success path)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            test_data = {
                "save_test": "success",
                "numbers": [1, 2, 3, 4, 5],
                "nested": {"key": "value"}
            }
            
            result = manager.save_data(test_data, tmp.name)
            assert result == True
            
            # Verify data was saved correctly
            with open(tmp.name, 'r') as f:
                saved_data = json.load(f)
                assert saved_data["save_test"] == "success"
                assert saved_data["numbers"] == [1, 2, 3, 4, 5]
                assert saved_data["nested"]["key"] == "value"
        
        os.unlink(tmp.name)
        
        # Test with invalid data (exercises serialization error branch)
        class UnserializableObject:
            pass
        
        invalid_data = {"object": UnserializableObject()}
        result = manager.save_data(invalid_data, "/tmp/test_invalid.json")
        assert result == False
        
        # Test with permission error (exercises permission error branch)
        result = manager.save_data({"test": "data"}, "/root/no_permission.json")
        assert result == False
        
        # Test with None data (exercises None data handling)
        result = manager.save_data(None, "/tmp/test_none.json")
        assert result == False
    
    def test_dataset_management_all_branches(self):
        """Test dataset management methods - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test add_dataset (exercises dataset addition)
        dataset_info = {
            "name": "test_dataset",
            "path": "/tmp/test_data",
            "type": "tilt_series",
            "metadata": {"pixel_size": 1.35, "voltage": 300}
        }
        
        dataset_id = manager.add_dataset(dataset_info)
        assert dataset_id is not None
        assert dataset_id in manager.datasets
        assert manager.datasets[dataset_id]["name"] == "test_dataset"
        
        # Test get_dataset (exercises dataset retrieval)
        retrieved = manager.get_dataset(dataset_id)
        assert retrieved is not None
        assert retrieved["name"] == "test_dataset"
        assert retrieved["metadata"]["pixel_size"] == 1.35
        
        # Test get_dataset with invalid ID (exercises not found branch)
        invalid_result = manager.get_dataset("invalid_id")
        assert invalid_result is None
        
        # Test set_current_dataset (exercises current dataset setting)
        manager.set_current_dataset(dataset_id)
        assert manager.current_dataset == dataset_id
        
        # Test get_current_dataset (exercises current dataset retrieval)
        current = manager.get_current_dataset()
        assert current is not None
        assert current["name"] == "test_dataset"
        
        # Test get_current_dataset with no current dataset
        manager.current_dataset = None
        current_none = manager.get_current_dataset()
        assert current_none is None
        
        # Test remove_dataset (exercises dataset removal)
        removed = manager.remove_dataset(dataset_id)
        assert removed == True
        assert dataset_id not in manager.datasets
        
        # Test remove_dataset with invalid ID (exercises not found branch)
        removed_invalid = manager.remove_dataset("invalid_id")
        assert removed_invalid == False
        
        # Test list_datasets (exercises dataset listing)
        # Add multiple datasets
        dataset1_id = manager.add_dataset({"name": "dataset1", "path": "/tmp/1"})
        dataset2_id = manager.add_dataset({"name": "dataset2", "path": "/tmp/2"})
        
        dataset_list = manager.list_datasets()
        assert len(dataset_list) == 2
        assert any(d["name"] == "dataset1" for d in dataset_list)
        assert any(d["name"] == "dataset2" for d in dataset_list)
    
    def test_metadata_management_all_branches(self):
        """Test metadata management - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test set_metadata (exercises metadata setting)
        manager.set_metadata("test_key", "test_value")
        assert manager.metadata["test_key"] == "test_value"
        
        # Test nested metadata setting
        manager.set_metadata("section.subsection.key", "nested_value")
        assert manager.metadata["section"]["subsection"]["key"] == "nested_value"
        
        # Test get_metadata (exercises metadata retrieval)
        value = manager.get_metadata("test_key")
        assert value == "test_value"
        
        # Test nested metadata retrieval
        nested_value = manager.get_metadata("section.subsection.key")
        assert nested_value == "nested_value"
        
        # Test get_metadata with default (exercises default branch)
        default_value = manager.get_metadata("non_existent_key", "default")
        assert default_value == "default"
        
        # Test get_metadata with no default (exercises None branch)
        none_value = manager.get_metadata("non_existent_key")
        assert none_value is None
    
    def test_cache_management_all_branches(self):
        """Test cache management - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test cache_data (exercises cache storage)
        test_data = {"cached": "data", "timestamp": "2023-01-01"}
        manager.cache_data("test_cache_key", test_data)
        assert "test_cache_key" in manager.data_cache
        assert manager.data_cache["test_cache_key"]["cached"] == "data"
        
        # Test get_cached_data (exercises cache retrieval)
        cached = manager.get_cached_data("test_cache_key")
        assert cached is not None
        assert cached["cached"] == "data"
        
        # Test get_cached_data with invalid key (exercises not found branch)
        invalid_cached = manager.get_cached_data("invalid_cache_key")
        assert invalid_cached is None
        
        # Test clear_cache (exercises cache clearing)
        manager.clear_cache()
        assert manager.data_cache == {}
        
        # Test cache with expiration
        manager.cache_data("expiring_key", {"data": "expires"}, ttl=1)
        assert "expiring_key" in manager.data_cache
        
        # Test cache size limit (exercises cache size management)
        for i in range(105):  # Exceed typical cache limit
            manager.cache_data(f"key_{i}", {"data": i})
        
        # Cache should have been cleaned up
        assert len(manager.data_cache) <= 100
    
    def test_data_validation_all_branches(self):
        """Test data validation - exercises ALL validation paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test validate_dataset_info (exercises validation logic)
        valid_info = {
            "name": "valid_dataset",
            "path": "/valid/path",
            "type": "tilt_series"
        }
        assert manager.validate_dataset_info(valid_info) == True
        
        # Test with missing required fields (exercises validation failure)
        invalid_info = {"name": "incomplete"}  # Missing path and type
        assert manager.validate_dataset_info(invalid_info) == False
        
        # Test with invalid type (exercises type validation)
        invalid_type_info = {
            "name": "invalid_type",
            "path": "/path",
            "type": "invalid_type"
        }
        assert manager.validate_dataset_info(invalid_type_info) == False
        
        # Test data integrity check
        test_data = {
            "version": "1.0",
            "checksum": "abc123",
            "data": [1, 2, 3, 4, 5]
        }
        
        integrity_result = manager.check_data_integrity(test_data)
        assert isinstance(integrity_result, bool)
    
    def test_import_export_all_branches(self):
        """Test import/export functionality - exercises ALL code paths."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Add test datasets
        dataset1_id = manager.add_dataset({
            "name": "export_test1",
            "path": "/tmp/test1",
            "type": "tilt_series"
        })
        dataset2_id = manager.add_dataset({
            "name": "export_test2", 
            "path": "/tmp/test2",
            "type": "tomogram"
        })
        
        # Test export_datasets (exercises export functionality)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            export_result = manager.export_datasets(tmp.name)
            assert export_result == True
            
            # Verify export content
            with open(tmp.name, 'r') as f:
                exported_data = json.load(f)
                assert "datasets" in exported_data
                assert len(exported_data["datasets"]) == 2
        
        os.unlink(tmp.name)
        
        # Test import_datasets (exercises import functionality)
        # Clear current datasets
        manager.datasets = {}
        
        # Create import data
        import_data = {
            "datasets": {
                "import_id1": {"name": "imported1", "path": "/import/1", "type": "tilt_series"},
                "import_id2": {"name": "imported2", "path": "/import/2", "type": "tomogram"}
            },
            "metadata": {"import_version": "1.0"}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            json.dump(import_data, tmp)
            tmp.flush()
            
            import_result = manager.import_datasets(tmp.name)
            assert import_result == True
            assert len(manager.datasets) == 2
            assert any(d["name"] == "imported1" for d in manager.datasets.values())
        
        os.unlink(tmp.name)
        
        # Test import with invalid file (exercises error handling)
        import_invalid = manager.import_datasets("/non/existent/file.json")
        assert import_invalid == False

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--cov=aretomo3_gui.data_management.data_manager", "--cov-report=term-missing"])
