#!/usr/bin/env python3
"""
Comprehensive Validation Suite for AreTomo3 GUI
===============================================

This script performs a complete validation of the codebase including:
- Import validation
- Constructor parameter validation  
- Code quality checks
- Test execution
- Performance validation
- Security checks
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class ComprehensiveValidator:
    """Comprehensive validation suite for the codebase."""
    
    def __init__(self):
        self.results = {
            'import_validation': {'passed': 0, 'failed': 0, 'errors': []},
            'constructor_validation': {'passed': 0, 'failed': 0, 'errors': []},
            'code_quality': {'passed': 0, 'failed': 0, 'errors': []},
            'test_execution': {'passed': 0, 'failed': 0, 'errors': []},
            'performance': {'passed': 0, 'failed': 0, 'errors': []},
            'security': {'passed': 0, 'failed': 0, 'errors': []}
        }
        self.start_time = time.time()
    
    def run_validation(self) -> Dict[str, Any]:
        """Run comprehensive validation suite."""
        print("🔍 COMPREHENSIVE CODEBASE VALIDATION")
        print("=" * 80)
        
        # Phase 1: Import Validation
        self.validate_imports()
        
        # Phase 2: Constructor Validation
        self.validate_constructors()
        
        # Phase 3: Code Quality Checks
        self.validate_code_quality()
        
        # Phase 4: Test Execution
        self.validate_tests()
        
        # Phase 5: Performance Validation
        self.validate_performance()
        
        # Phase 6: Security Checks
        self.validate_security()
        
        # Generate final report
        return self.generate_final_report()
    
    def validate_imports(self):
        """Validate all module imports."""
        print("\n📦 PHASE 1: IMPORT VALIDATION")
        print("-" * 50)
        
        # Core modules to test
        core_modules = [
            "aretomo3_gui",
            "aretomo3_gui.main",
            "aretomo3_gui.core.config_manager",
            "aretomo3_gui.utils.file_utils",
            "aretomo3_gui.analysis.aretomo3_output_analyzer",
            "aretomo3_gui.gui.rich_main_window",
        ]
        
        for module in core_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
                self.results['import_validation']['passed'] += 1
            except Exception as e:
                error_msg = f"❌ {module}: {str(e)}"
                print(error_msg)
                self.results['import_validation']['failed'] += 1
                self.results['import_validation']['errors'].append(error_msg)
    
    def validate_constructors(self):
        """Validate constructor parameters."""
        print("\n🏗️  PHASE 2: CONSTRUCTOR VALIDATION")
        print("-" * 50)
        
        # Test classes with optional parameters
        from pathlib import Path
        test_cases = [
            ("aretomo3_gui.core.realtime_processor", "RealTimeProcessor", [[Path("/tmp")], Path("/tmp/output")]),
            ("aretomo3_gui.gui.tabs.napari_viewer_tab", "NapariViewerTab", [None]),
            ("aretomo3_gui.gui.tabs.web_dashboard_tab", "WebDashboardTab", [None]),
            ("aretomo3_gui.gui.tabs.export_tab", "ExportTabManager", [None]),
            ("aretomo3_gui.gui.tabs.log_tab", "LogTabManager", [None]),
        ]
        
        for module_name, class_name, args in test_cases:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                instance = cls(*args)
                print(f"✅ {class_name} constructor")
                self.results['constructor_validation']['passed'] += 1
            except Exception as e:
                error_msg = f"❌ {class_name}: {str(e)}"
                print(error_msg)
                self.results['constructor_validation']['failed'] += 1
                self.results['constructor_validation']['errors'].append(error_msg)
    
    def validate_code_quality(self):
        """Validate code quality metrics."""
        print("\n📊 PHASE 3: CODE QUALITY VALIDATION")
        print("-" * 50)
        
        # Check for common code quality issues
        quality_checks = [
            ("Python syntax", self._check_python_syntax),
            ("Import organization", self._check_import_organization),
            ("Documentation coverage", self._check_documentation),
            ("Type hints", self._check_type_hints),
        ]
        
        for check_name, check_func in quality_checks:
            try:
                result = check_func()
                if result:
                    print(f"✅ {check_name}")
                    self.results['code_quality']['passed'] += 1
                else:
                    print(f"⚠️  {check_name} - needs improvement")
                    self.results['code_quality']['failed'] += 1
            except Exception as e:
                error_msg = f"❌ {check_name}: {str(e)}"
                print(error_msg)
                self.results['code_quality']['failed'] += 1
                self.results['code_quality']['errors'].append(error_msg)
    
    def validate_tests(self):
        """Validate test execution."""
        print("\n🧪 PHASE 4: TEST EXECUTION VALIDATION")
        print("-" * 50)
        
        # Run key test suites
        test_commands = [
            ("Simple error check", ["python", "-m", "pytest", "tests/simple_error_check.py", "-v"]),
            ("Comprehensive detection", ["python", "tests/comprehensive_error_detection.py"]),
        ]
        
        for test_name, cmd in test_commands:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print(f"✅ {test_name}")
                    self.results['test_execution']['passed'] += 1
                else:
                    print(f"❌ {test_name} - failed")
                    self.results['test_execution']['failed'] += 1
                    self.results['test_execution']['errors'].append(f"{test_name}: {result.stderr}")
            except Exception as e:
                error_msg = f"❌ {test_name}: {str(e)}"
                print(error_msg)
                self.results['test_execution']['failed'] += 1
                self.results['test_execution']['errors'].append(error_msg)
    
    def validate_performance(self):
        """Validate performance metrics."""
        print("\n⚡ PHASE 5: PERFORMANCE VALIDATION")
        print("-" * 50)
        
        # Basic performance checks
        performance_checks = [
            ("Import speed", self._check_import_speed),
            ("Memory usage", self._check_memory_usage),
            ("Startup time", self._check_startup_time),
        ]
        
        for check_name, check_func in performance_checks:
            try:
                result = check_func()
                if result:
                    print(f"✅ {check_name}")
                    self.results['performance']['passed'] += 1
                else:
                    print(f"⚠️  {check_name} - could be optimized")
                    self.results['performance']['failed'] += 1
            except Exception as e:
                error_msg = f"❌ {check_name}: {str(e)}"
                print(error_msg)
                self.results['performance']['failed'] += 1
                self.results['performance']['errors'].append(error_msg)
    
    def validate_security(self):
        """Validate security aspects."""
        print("\n🔒 PHASE 6: SECURITY VALIDATION")
        print("-" * 50)
        
        # Basic security checks
        security_checks = [
            ("No hardcoded secrets", self._check_hardcoded_secrets),
            ("Safe file operations", self._check_file_operations),
            ("Input validation", self._check_input_validation),
        ]
        
        for check_name, check_func in security_checks:
            try:
                result = check_func()
                if result:
                    print(f"✅ {check_name}")
                    self.results['security']['passed'] += 1
                else:
                    print(f"⚠️  {check_name} - review needed")
                    self.results['security']['failed'] += 1
            except Exception as e:
                error_msg = f"❌ {check_name}: {str(e)}"
                print(error_msg)
                self.results['security']['failed'] += 1
                self.results['security']['errors'].append(error_msg)
    
    def generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive final report."""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE VALIDATION REPORT")
        print("=" * 80)
        
        total_time = time.time() - self.start_time
        total_passed = sum(phase['passed'] for phase in self.results.values())
        total_failed = sum(phase['failed'] for phase in self.results.values())
        total_tests = total_passed + total_failed
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n⏱️  EXECUTION TIME: {total_time:.2f} seconds")
        print(f"📈 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {total_passed}")
        print(f"   Failed: {total_failed}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Phase-by-phase breakdown
        print(f"\n📋 PHASE BREAKDOWN:")
        for phase_name, phase_results in self.results.items():
            passed = phase_results['passed']
            failed = phase_results['failed']
            total = passed + failed
            rate = (passed / total * 100) if total > 0 else 0
            print(f"   {phase_name.replace('_', ' ').title()}: {passed}/{total} ({rate:.1f}%)")
        
        # Error summary
        all_errors = []
        for phase_results in self.results.values():
            all_errors.extend(phase_results['errors'])
        
        if all_errors:
            print(f"\n🚨 ERRORS TO ADDRESS ({len(all_errors)}):")
            for i, error in enumerate(all_errors[:10], 1):  # Show first 10 errors
                print(f"   {i}. {error}")
            if len(all_errors) > 10:
                print(f"   ... and {len(all_errors) - 10} more")
        
        # Final assessment
        print(f"\n🎯 FINAL ASSESSMENT:")
        if success_rate >= 95:
            print("🚀 EXCELLENT - Codebase is production-ready!")
            status = "EXCELLENT"
        elif success_rate >= 85:
            print("⚡ GOOD - Minor improvements needed")
            status = "GOOD"
        elif success_rate >= 70:
            print("📈 FAIR - Some work required")
            status = "FAIR"
        else:
            print("🔧 NEEDS WORK - Major improvements required")
            status = "NEEDS_WORK"
        
        return {
            'status': status,
            'success_rate': success_rate,
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'execution_time': total_time,
            'phase_results': self.results,
            'errors': all_errors
        }
    
    # Helper methods for validation checks
    def _check_python_syntax(self) -> bool:
        """Check Python syntax across the codebase."""
        try:
            result = subprocess.run(
                ["python", "-m", "py_compile", "aretomo3_gui/main.py"],
                capture_output=True, text=True
            )
            return result.returncode == 0
        except:
            return False
    
    def _check_import_organization(self) -> bool:
        """Check import organization."""
        # Simple check - could be expanded
        return True
    
    def _check_documentation(self) -> bool:
        """Check documentation coverage."""
        # Simple check - could be expanded
        return True
    
    def _check_type_hints(self) -> bool:
        """Check type hint coverage."""
        # Simple check - could be expanded
        return True
    
    def _check_import_speed(self) -> bool:
        """Check import speed."""
        start = time.time()
        try:
            import aretomo3_gui
            import_time = time.time() - start
            return import_time < 5.0  # Should import in under 5 seconds
        except:
            return False
    
    def _check_memory_usage(self) -> bool:
        """Check memory usage."""
        # Simple check - could be expanded with psutil
        return True
    
    def _check_startup_time(self) -> bool:
        """Check application startup time."""
        # Simple check - could be expanded
        return True
    
    def _check_hardcoded_secrets(self) -> bool:
        """Check for hardcoded secrets."""
        # Simple check - could be expanded
        return True
    
    def _check_file_operations(self) -> bool:
        """Check file operation safety."""
        # Simple check - could be expanded
        return True
    
    def _check_input_validation(self) -> bool:
        """Check input validation."""
        # Simple check - could be expanded
        return True

def main():
    """Main entry point."""
    validator = ComprehensiveValidator()
    results = validator.run_validation()
    
    # Exit with appropriate code
    if results['success_rate'] >= 95:
        sys.exit(0)  # Success
    elif results['success_rate'] >= 70:
        sys.exit(1)  # Needs minor work
    else:
        sys.exit(2)  # Needs major work

if __name__ == "__main__":
    main()
