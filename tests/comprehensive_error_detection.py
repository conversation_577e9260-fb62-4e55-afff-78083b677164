#!/usr/bin/env python3
"""
Comprehensive Error Detection and Testing Suite
Identifies all errors in the AreTomo3 GUI codebase for 200% completion.
"""

import os
import sys
import traceback
import importlib.util
from pathlib import Path

# Set environment for testing
os.environ["QT_QPA_PLATFORM"] = "offscreen"
sys.path.insert(0, ".")

class ComprehensiveErrorDetector:
    """Comprehensive error detection and testing system."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.passed_tests = []
        
    def test_import(self, module_name, description=""):
        """Test importing a module."""
        try:
            __import__(module_name)
            self.passed_tests.append(f"✅ {description or module_name}")
            return True
        except Exception as e:
            error_msg = f"❌ {description or module_name}: {str(e)}"
            self.errors.append(error_msg)
            print(error_msg)
            return False
    
    def test_class_instantiation(self, module_name, class_name, description=""):
        """Test instantiating a class."""
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            # Don't actually instantiate GUI classes to avoid display issues
            if 'GUI' in class_name or 'Window' in class_name:
                self.passed_tests.append(f"✅ {description or f'{module_name}.{class_name}'} (class available)")
                return True
            else:
                instance = cls()
                self.passed_tests.append(f"✅ {description or f'{module_name}.{class_name}'}")
                return True
        except Exception as e:
            error_msg = f"❌ {description or f'{module_name}.{class_name}'}: {str(e)}"
            self.errors.append(error_msg)
            print(error_msg)
            return False
    
    def test_priority_features(self):
        """Test all 10 priority features."""
        print("\n🎯 TESTING PRIORITY FEATURES:")
        
        features = [
            ("aretomo3_gui.core.realtime_processor", "RealTimeProcessor", "1. Real-time Processing"),
            ("aretomo3_gui.core.automation.workflow_manager", "WorkflowManager", "2. Workflow Management"),
            ("aretomo3_gui.gui.tabs.napari_viewer_tab", "NapariViewerTab", "3. 3D Visualization"),
            ("aretomo3_gui.data_management.data_manager", "DataManager", "4. Data Management"),
            ("aretomo3_gui.formats.format_manager", "FormatManager", "5. Multi-format Support"),
            ("aretomo3_gui.particle_picking.picker", "ParticlePicker", "6. Particle Picking"),
            ("aretomo3_gui.subtomogram.averaging", "SubtomogramAverager", "7. Subtomogram Averaging"),
            ("aretomo3_gui.integration.external_tools", "ExternalToolsManager", "8. External Integration"),
            ("aretomo3_gui.web.server", "WebServer", "9. Web Interface"),
            ("aretomo3_gui.analytics.advanced_analytics", "AdvancedAnalytics", "10. Advanced Analytics")
        ]
        
        for module, cls, desc in features:
            self.test_class_instantiation(module, cls, desc)
    
    def test_gui_components(self):
        """Test GUI components."""
        print("\n🖥️  TESTING GUI COMPONENTS:")
        
        components = [
            ("aretomo3_gui.gui.rich_main_window", "RichAreTomoGUI", "Rich Main Window"),
            ("aretomo3_gui.gui.main_window", "AreTomoGUI", "Standard Main Window"),
            ("aretomo3_gui.gui.tabs.unified_analysis_tab", "UnifiedAnalysisTab", "Analysis Workbench"),
            ("aretomo3_gui.gui.tabs.web_dashboard_tab", "WebDashboardTab", "Web Dashboard"),
            ("aretomo3_gui.gui.widgets.batch_processing", "BatchProcessingWidget", "Batch Processing"),
            ("aretomo3_gui.gui.tabs.realtime_analysis_tab", "RealTimeAnalysisTab", "Real-time Analysis"),
            ("aretomo3_gui.gui.tabs.napari_viewer_tab", "NapariViewerTab", "Napari 3D Viewer"),
            ("aretomo3_gui.gui.tabs.export_tab", "ExportTabManager", "Export Manager"),
            ("aretomo3_gui.gui.tabs.log_tab", "LogTabManager", "Log Manager")
        ]
        
        for module, cls, desc in components:
            self.test_class_instantiation(module, cls, desc)
    
    def test_core_system(self):
        """Test core system components."""
        print("\n🔧 TESTING CORE SYSTEM:")
        
        core_modules = [
            ("aretomo3_gui.main", "Main Entry Point"),
            ("aretomo3_gui.qt_backend_init", "Qt Backend"),
            ("aretomo3_gui.core.config_manager", "Configuration Manager"),
            ("aretomo3_gui.core.error_handling", "Error Handling"),
            ("aretomo3_gui.core.resource_manager", "Resource Manager"),
            ("aretomo3_gui.core.thread_manager", "Thread Manager"),
            ("aretomo3_gui.utils.file_utils", "File Utilities"),
            ("aretomo3_gui.analysis.realtime_monitor", "Real-time Monitor")
        ]
        
        for module, desc in core_modules:
            self.test_import(module, desc)
    
    def test_web_components(self):
        """Test web interface components."""
        print("\n🌐 TESTING WEB COMPONENTS:")
        
        web_components = [
            ("aretomo3_gui.web.server", "WebServer", "Web Server"),
            ("aretomo3_gui.web.plot_server", None, "Plot Server"),
            ("aretomo3_gui.web.api_server", None, "API Server")
        ]

        for module, cls, desc in web_components:
            if cls:
                self.test_class_instantiation(module, cls, desc)
            else:
                self.test_import(module, desc)
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests."""
        print("🔍 COMPREHENSIVE ERROR DETECTION & TESTING")
        print("=" * 80)

        # Test all components
        self.test_core_system()
        self.test_priority_features()
        self.test_gui_components()
        self.test_web_components()

        # Generate report and return success rate
        return self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report."""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        total_tests = len(self.passed_tests) + len(self.errors)
        success_rate = (len(self.passed_tests) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 SUMMARY:")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {len(self.passed_tests)}")
        print(f"Failed: {len(self.errors)}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if self.errors:
            print(f"\n🚨 ERRORS TO FIX ({len(self.errors)}):")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        print(f"\n🎯 COMPLETION STATUS:")
        if success_rate >= 95:
            print("🚀 EXCELLENT - Ready for 200% completion!")
        elif success_rate >= 85:
            print("⚡ GOOD - Minor fixes needed")
        elif success_rate >= 70:
            print("📈 FAIR - Some work required")
        else:
            print("🔧 NEEDS WORK - Major fixes required")
        
        return success_rate

if __name__ == "__main__":
    detector = ComprehensiveErrorDetector()
    success_rate = detector.run_comprehensive_tests()
    
    # Exit with appropriate code
    if success_rate >= 95:
        sys.exit(0)  # Success
    else:
        sys.exit(1)  # Needs work
