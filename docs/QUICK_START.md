# AreTomo3 GUI Professional - Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get you up and running with AreTomo3 GUI Professional in just a few minutes.

---

## 📦 Step 1: Installation

### **Option A: One-Click Installation (Recommended)**
```bash
# Download and extract the package
unzip AreTomo3-GUI-Professional-v3.0.0.zip
cd AreTomo3-GUI-Professional-v3.0.0

# Run the installer
python install.py --venv --test
```

### **Option B: Platform-Specific Scripts**
```bash
# Linux/macOS
./install.sh

# Windows
install.bat
```

### **Option C: Development Install**
```bash
# For development work
pip install -r requirements.txt
pip install -e .
python -m aretomo3_gui
```

---

## 🎯 Step 2: First Launch

### **Activate Environment and Launch**
```bash
# Activate virtual environment (if using --venv)
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Launch AreTomo3 GUI
python -m aretomo3_gui
```

### **What You'll See**
- 🖥️ **Main Window**: Professional dark theme interface
- 📊 **Dashboard**: Real-time processing status
- 🔧 **Settings Panel**: Configuration options
- 📁 **File Browser**: Input/output management

---

## ⚙️ Step 3: Basic Configuration

### **Essential Settings**
1. **AreTomo3 Executable Path**
   - Go to `Settings` → `AreTomo3 Configuration`
   - Browse and select your AreTomo3 executable
   - Click `Test Connection` to verify

2. **Default Directories**
   - Set `Input Directory`: Where your tilt series are located
   - Set `Output Directory`: Where results will be saved
   - Set `Temporary Directory`: For intermediate files

3. **Processing Parameters**
   - `Pixel Size`: Your detector pixel size (Å)
   - `Voltage`: Microscope acceleration voltage (kV)
   - `Cs`: Spherical aberration coefficient (mm)

---

## 🔬 Step 4: Your First Processing Job

### **Load Data**
1. Click `File` → `Open Tilt Series`
2. Navigate to your `.mrc` or `.st` file
3. The file will appear in the `Input Files` list

### **Set Parameters**
1. **Basic Parameters**:
   - Pixel size: `1.0` Å (example)
   - Voltage: `300` kV
   - Tilt range: `-60` to `+60` degrees

2. **Advanced Options** (optional):
   - Alignment method: `Patch tracking`
   - Reconstruction method: `SART`
   - Number of iterations: `15`

### **Start Processing**
1. Click the `Start Processing` button
2. Monitor progress in the `Real-time Monitor` tab
3. View results in the `3D Viewer` when complete

---

## 🧪 Step 5: Validation

### **Test Your Installation**
```bash
# Run comprehensive validation
python tests/comprehensive_validation.py

# Expected output: 100% success rate
```

### **Quick Test**
```bash
# Test basic import
python -c "import aretomo3_gui; print('✅ Installation successful!')"

# Test GUI components
python -c "
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
from aretomo3_gui.gui.rich_main_window import RichAreTomoGUI
from PyQt6.QtWidgets import QApplication
app = QApplication([])
window = RichAreTomoGUI()
print('✅ GUI components working')
app.quit()
"
```

---

## 🔧 Troubleshooting

### **Common Issues**

#### **AreTomo3 Not Found**
```bash
# Error: AreTomo3 executable not found
# Solution: Set correct path in Settings
```

#### **Permission Errors**
```bash
# Error: Cannot write to output directory
# Solution: Check directory permissions
chmod 755 /path/to/output/directory
```

#### **Import Errors**
```bash
# Error: Module not found
# Solution: Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

### **Getting Help**
- 📖 **Documentation**: Check `docs/` directory
- 🐛 **Issues**: Report on GitHub
- 📧 **Support**: <EMAIL>

---

## 🎯 Next Steps

### **Learn More**
- 📖 **[Installation Guide](installation/INSTALLATION_GUIDE.md)**: Detailed setup
- 🔧 **[User Manual](user_guide/USER_MANUAL.md)**: Complete features
- 💡 **[Examples](../examples/)**: Usage examples

### **Advanced Features**
- **Batch Processing**: Process multiple datasets
- **Web Interface**: Remote monitoring
- **3D Visualization**: Interactive volume viewer
- **Quality Assessment**: Automated metrics

---

## ✅ Success Checklist

- [ ] ✅ Installation completed successfully
- [ ] ✅ AreTomo3 executable configured
- [ ] ✅ Input/output directories set
- [ ] ✅ First tilt series processed
- [ ] ✅ Results viewed in 3D viewer
- [ ] ✅ Validation tests passed

---

**🎉 Congratulations!** You're now ready to use AreTomo3 GUI Professional for your cryo-electron tomography projects.

**Status**: ✅ **Ready for Production Use**
**Support**: 📧 <EMAIL>
**Documentation**: 📖 Complete guides in `docs/` directory
