# AreTomo3 GUI Project Structure Report

Generated on: 1749485314.9761078

## Directory Tree

```
AT3GUI_devel/
├── aretomo3_gui
│   ├── analysis
│   │   ├── ctf_analysis
│   │   ├── motion_analysis
│   │   ├── __init__.py
│   │   ├── aretomo3_output_analyzer.py
│   │   ├── auto_plot_generator.py
│   │   ├── interactive_plots.py
│   │   └── realtime_monitor.py
│   ├── analytics
│   │   ├── __init__.py
│   │   ├── advanced_analytics.py
│   │   └── analyzer.py
│   ├── core
│   │   ├── automation
│   │   ├── config
│   │   ├── __init__.py
│   │   ├── advanced_logging.py
│   │   ├── backup_system.py
│   │   ├── config_manager.py
│   │   ├── continue_mode_manager.py
│   │   ├── data_validation.py
│   │   ├── database_manager.py
│   │   ├── dependency_check.py
│   │   ├── enhanced_database_manager.py
│   │   ├── enhanced_parameters.py
│   │   ├── enhanced_realtime_processor.py
│   │   ├── error_handling.py
│   │   ├── error_recovery.py
│   │   ├── file_organization.py
│   │   ├── file_watcher.py
│   │   ├── logging_config.py
│   │   ├── memory_manager.py
│   │   ├── multi_format_handler.py
│   │   ├── performance_monitor.py
│   │   ├── performance_optimizer.py
│   │   ├── plugin_system.py
│   │   ├── realtime_processor.py
│   │   ├── resource_manager.py
│   │   ├── results_tracker.py
│   │   ├── secure_web_api.py
│   │   ├── security_framework.py
│   │   ├── session_manager.py
│   │   ├── system_integration.py
│   │   ├── system_monitor.py
│   │   ├── thread_manager.py
│   │   └── tilt_series.py
│   ├── data_management
│   │   ├── __init__.py
│   │   ├── data_manager.py
│   │   └── manager.py
│   ├── formats
│   │   ├── __init__.py
│   │   ├── format_manager.py
│   │   └── manager.py
│   ├── gui
│   │   ├── analysis
│   │   ├── components
│   │   ├── embedded_viewers
│   │   ├── tabs
│   │   ├── themes
│   │   ├── viewers
│   │   ├── visualizers
│   │   ├── widgets
│   │   ├── __init__.py
│   │   ├── advanced_settings_tab.py
│   │   ├── main_window.py
│   │   ├── minimal_gui.py
│   │   ├── plot_theme_manager.py
│   │   ├── rich_main_window.py
│   │   ├── theme_manager.py
│   │   └── themes.py
│   ├── integration
│   │   ├── __init__.py
│   │   ├── external_tools.py
│   │   └── manager.py
│   ├── particle_picking
│   │   ├── __init__.py
│   │   └── picker.py
│   ├── realtime
│   │   ├── __init__.py
│   │   └── processor.py
│   ├── subtomogram
│   │   ├── __init__.py
│   │   └── averaging.py
│   ├── tools
│   │   ├── __init__.py
│   │   └── kmeans_integration.py
│   ├── utils
│   │   ├── __init__.py
│   │   ├── aretomo3_parser.py
│   │   ├── aretomo3_wrapper.sh
│   │   ├── documentation_generator.py
│   │   ├── eer_reader.py
│   │   ├── eer_reader_new.py
│   │   ├── export_functions.py
│   │   ├── file_utils.py
│   │   ├── mdoc_parser.py
│   │   ├── pdf_report_generator.py
│   │   ├── utils.py
│   │   └── warning_suppression.py
│   ├── visualization
│   │   ├── __init__.py
│   │   └── engine.py
│   ├── web
│   │   ├── templates
│   │   ├── __init__.py
│   │   ├── api_server.py
│   │   ├── plot_server.py
│   │   └── server.py
│   ├── web_interface
│   │   ├── __init__.py
│   │   └── server.py
│   ├── workflow
│   │   ├── __init__.py
│   │   └── engine.py
│   ├── __init__.py
│   ├── __main__.py
│   ├── cli.py
│   ├── main.py
│   └── qt_backend_init.py
├── config
│   └── default_config.json
├── data
├── docs
│   ├── api
│   ├── developer
│   ├── examples
│   ├── images
│   ├── installation
│   ├── user_guide
│   ├── COMPREHENSIVE_FEATURE_GUIDE.md
│   ├── INSTALLATION_GUIDE.md
│   └── QUICK_START.md
├── documentation
│   ├── guides
│   └── reports
│       ├── COMPREHENSIVE_FEATURE_ANALYSIS_AND_SUGGESTIONS.md
│       ├── DEPLOYMENT_SUCCESS_SUMMARY.md
│       ├── FINAL_SUCCESS_SUMMARY.md
│       ├── FINAL_TEST_COVERAGE_SUMMARY.md
│       ├── PROFESSIONAL_DISTRIBUTION_SUCCESS.md
│       └── TEST_COVERAGE_IMPROVEMENT_PLAN.md
├── examples
│   ├── __init__.py
│   ├── api_usage.py
│   └── basic_usage.py
├── output
├── resources
│   ├── fonts
│   ├── icons
│   ├── sounds
│   ├── templates
│   └── themes
├── scripts
│   ├── __init__.py
│   └── organize_structure.py
├── tests
│   ├── core
│   │   ├── __init__.py
│   │   ├── test_core_functionality.py
│   │   ├── test_error_handling.py
│   │   ├── test_fixed_logging.py
│   │   ├── test_resource_manager.py
│   │   └── test_tilt_series.py
│   ├── fixtures
│   ├── functional
│   ├── gui
│   │   ├── __init__.py
│   │   ├── test___init__.py
│   │   ├── test___main__.py
│   │   ├── test_advanced_file_browser.py
│   │   ├── test_advanced_logging.py
│   │   ├── test_advanced_progress_widget.py
│   │   ├── test_advanced_settings_tab.py
│   │   ├── test_analysis_tab.py
│   │   ├── test_analysis_viewer.py
│   │   ├── test_analyzer.py
│   │   ├── test_api_server.py
│   │   ├── test_aretomo3_output_analyzer.py
│   │   ├── test_aretomo3_parser.py
│   │   ├── test_auto_plot_generator.py
│   │   ├── test_backup_system.py
│   │   ├── test_batch_tab.py
│   │   ├── test_cli.py
│   │   ├── test_config.py
│   │   ├── test_config_manager.py
│   │   ├── test_config_validation.py
│   │   ├── test_continue_mode_manager.py
│   │   ├── test_ctf_dashboard.py
│   │   ├── test_ctf_parser.py
│   │   ├── test_ctf_quality.py
│   │   ├── test_ctf_tab.py
│   │   ├── test_ctf_utils.py
│   │   ├── test_ctf_viewer.py
│   │   ├── test_ctf_visualizer.py
│   │   ├── test_data_validation.py
│   │   ├── test_database_manager.py
│   │   ├── test_dependency_check.py
│   │   ├── test_documentation_generator.py
│   │   ├── test_eer_reader.py
│   │   ├── test_eer_reader_new.py
│   │   ├── test_engine.py
│   │   ├── test_enhanced_analysis_tab.py
│   │   ├── test_enhanced_database_manager.py
│   │   ├── test_enhanced_monitor_tab.py
│   │   ├── test_enhanced_parameters.py
│   │   ├── test_enhanced_parameters_tab.py
│   │   ├── test_enhanced_progress_visualization.py
│   │   ├── test_enhanced_realtime_processor.py
│   │   ├── test_enhanced_spinbox.py
│   │   ├── test_error_recovery.py
│   │   ├── test_export_functions.py
│   │   ├── test_export_tab.py
│   │   ├── test_file_organization.py
│   │   ├── test_file_watcher.py
│   │   ├── test_gpu_manager_widget.py
│   │   ├── test_interactive_plots.py
│   │   ├── test_kmeans_integration.py
│   │   ├── test_live_processing_tab.py
│   │   ├── test_live_tilt_series_monitor.py
│   │   ├── test_log_tab.py
│   │   ├── test_logging_config.py
│   │   ├── test_main.py
│   │   ├── test_main_tab.py
│   │   ├── test_main_window.py
│   │   ├── test_manager.py
│   │   ├── test_mdoc_parser.py
│   │   ├── test_memory_manager.py
│   │   ├── test_menu_manager.py
│   │   ├── test_minimal_gui.py
│   │   ├── test_mock_napari_viewer.py
│   │   ├── test_monitor_tab.py
│   │   ├── test_motion_correction_visualizer.py
│   │   ├── test_motion_parser.py
│   │   ├── test_motion_viewer.py
│   │   ├── test_motion_visualizer.py
│   │   ├── test_mrc_viewer.py
│   │   ├── test_multi_format_handler.py
│   │   ├── test_multigpu_manager.py
│   │   ├── test_napari_mrc_viewer.py
│   │   ├── test_napari_viewer.py
│   │   ├── test_parameter_manager.py
│   │   ├── test_parameter_optimizer.py
│   │   ├── test_pdf_report_generator.py
│   │   ├── test_performance_monitor.py
│   │   ├── test_performance_optimizer.py
│   │   ├── test_plot_server.py
│   │   ├── test_plot_theme_manager.py
│   │   ├── test_plugin_system.py
│   │   ├── test_preview_grid.py
│   │   ├── test_processing_dashboard.py
│   │   ├── test_processor.py
│   │   ├── test_project_management.py
│   │   ├── test_qt_backend_init.py
│   │   ├── test_quality_predictor.py
│   │   ├── test_real_time_analyzer.py
│   │   ├── test_realtime_analysis_tab.py
│   │   ├── test_realtime_monitor.py
│   │   ├── test_realtime_widget.py
│   │   ├── test_reorganized_main_tab.py
│   │   ├── test_reorganized_main_tab_backup.py
│   │   ├── test_resource_monitor.py
│   │   ├── test_results_tracker.py
│   │   ├── test_secure_web_api.py
│   │   ├── test_security_framework.py
│   │   ├── test_server.py
│   │   ├── test_session_manager.py
│   │   ├── test_smart_file_organizer.py
│   │   ├── test_system_integration.py
│   │   ├── test_system_monitor.py
│   │   ├── test_template_manager.py
│   │   ├── test_theme_manager.py
│   │   ├── test_themes.py
│   │   ├── test_thread_manager.py
│   │   ├── test_unified_live_processing_tab.py
│   │   ├── test_unified_processing_monitor.py
│   │   ├── test_utils.py
│   │   ├── test_viewer_tab.py
│   │   ├── test_visualization.py
│   │   ├── test_warning_suppression.py
│   │   └── test_web_server_widget.py
│   ├── integration
│   │   ├── __init__.py
│   │   ├── BATCH_PROCESSING_INVESTIGATION.py
│   │   ├── bypass_test.py
│   │   ├── FRESH_INSTALL_TEST.py
│   │   ├── minimal_import_test.py
│   │   ├── simple_gui_test.py
│   │   ├── SIMPLE_TEST.py
│   │   ├── test_at3gui_workflow.py
│   │   ├── test_comprehensive.py
│   │   ├── test_comprehensive_imports.py
│   │   ├── test_files_tab_monitoring.py
│   │   ├── test_final_verification.py
│   │   ├── test_implementation.py
│   │   ├── test_processing.py
│   │   ├── test_progress_direct.py
│   │   ├── test_progress_parsing.py
│   │   ├── test_progress_tracking.py
│   │   ├── VENV_COMPREHENSIVE_TEST.py
│   │   ├── VENV_TEST_REPORT.json
│   │   └── widget_import_test.py
│   ├── performance
│   ├── priority
│   │   ├── __init__.py
│   │   ├── test_advanced_analytics.py
│   │   ├── test_averaging.py
│   │   ├── test_data_manager.py
│   │   ├── test_external_tools.py
│   │   ├── test_format_manager.py
│   │   ├── test_napari_viewer_tab.py
│   │   ├── test_picker.py
│   │   ├── test_rich_main_window.py
│   │   ├── test_server.py
│   │   ├── test_unified_analysis_tab.py
│   │   ├── test_web_dashboard_tab.py
│   │   └── test_workflow_manager.py
│   ├── reports
│   ├── unit
│   │   ├── tools
│   │   ├── __init__.py
│   │   ├── test_batch_processing.py
│   │   ├── test_file_utils.py
│   │   └── test_syntax_verification.py
│   ├── __init__.py
│   ├── comprehensive_error_detection.py
│   ├── comprehensive_validation.py
│   ├── conftest.py
│   ├── README.md
│   ├── simple_error_check.py
│   ├── test_analytics.py
│   ├── test_comprehensive_real_coverage.py
│   ├── test_core_functionality.py
│   ├── test_core_real_coverage.py
│   ├── test_gui_integration.py
│   ├── test_gui_real_coverage.py
│   ├── test_priority_features.py
│   ├── test_real_analysis.py
│   ├── test_real_analytics.py
│   ├── test_real_core.py
│   ├── test_real_data_management.py
│   ├── test_real_file_utils.py
│   ├── test_real_formats.py
│   ├── test_real_gui.py
│   ├── test_real_integration.py
│   ├── test_real_processing.py
│   ├── test_real_web.py
│   ├── test_realtime_processor.py
│   └── test_web_server.py
├── CLEAN_DIRECTORY_SUMMARY.json
├── CODEBASE_IMPROVEMENT_SUMMARY.md
├── install.py
├── LICENSE
├── MANIFEST.in
├── PROFESSIONAL_DIRECTORY_ORGANIZATION_COMPLETE.md
├── pyproject.toml
├── README.md
└── requirements.txt
```

## Structure Description

- **aretomo3_gui/**: Main application package containing all source code
- **docs/**: Comprehensive documentation including installation, user guides, and API reference
- **tests/**: Complete test suite with unit, integration, and performance tests
- **examples/**: Usage examples and tutorials for users and developers
- **scripts/**: Utility scripts for building, deployment, and maintenance
- **resources/**: Static resources including icons, themes, and templates
- **config/**: Configuration templates and default settings
- **data/**: Sample data files for testing and examples

## Key Files

- **README.md**: Main project documentation and quick start guide
- **install.py**: Professional installation script with dependency management
- **pyproject.toml**: Modern Python project configuration
- **requirements.txt**: Python dependencies specification
- **LICENSE**: Project license information