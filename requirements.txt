# AreTomo3 GUI Requirements - Professional Grade
# Core GUI Framework
PyQt6>=6.4.0
PyQt6-WebEngine>=6.4.0
qtpy>=2.0.0

# Scientific Computing Stack
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0
pandas>=1.3.0
scikit-image>=0.19.0
scikit-learn>=1.0.0

# File Format Support
mrcfile>=1.4.0
h5py>=3.6.0
tifffile>=2021.7.2
Pillow>=8.3.0
imageio>=2.19.0

# 3D Visualization
napari[pyqt6]>=0.4.18
vispy>=0.12.0
pyqtgraph>=0.13.0

# Web Framework & API (Updated versions)
flask>=2.0.0
uvicorn[standard]>=0.24.0
fastapi>=0.95.0
PyJWT>=2.3.0
websockets>=11.0
python-multipart>=0.0.5
pydantic>=1.10.0

# File System Monitoring
watchdog>=2.1.0

# Interactive Plotting (Updated versions)
plotly>=5.17.0
dash>=2.14.0
bokeh>=2.4.0
ipywidgets>=7.6.0
seaborn>=0.11.0

# System & Network
psutil>=5.8.0
requests>=2.25.0
aiohttp>=3.8.0

# Computer Vision & Image Processing
opencv-python>=4.5.0
scikit-image>=0.19.0

# Configuration and Data
PyYAML>=6.0
typing-extensions>=4.0.0
qrcode[pil]>=7.4.0

# Development & Testing
pytest>=6.0.0
pytest-qt>=4.0.0
pytest-cov>=3.0.0
pytest-mock>=3.6.0
black>=21.0.0
isort>=5.0.0
mypy>=0.910
flake8>=3.8.0
pre-commit>=2.15.0
