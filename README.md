# AreTomo3 GUI Professional

## 🚀 Simple Installation

```bash
# Install
python install.py

# Run
python -m aretomo3_gui
```

## 📁 Simple Structure

```
AreTomo3-GUI/
├── aretomo3_gui/          # Main application
├── docs/                  # Documentation  
├── tests/                 # Tests
├── examples/              # Usage examples
├── config/                # Configuration
├── install.py             # Simple installer
├── requirements.txt       # Dependencies
└── README.md             # This file
```

## 📚 Documentation

- **Quick Start**: `docs/QUICK_START.md`
- **Installation**: `docs/INSTALLATION_GUIDE.md`
- **Features**: `docs/COMPREHENSIVE_FEATURE_GUIDE.md`

## 🧪 Testing

```bash
python -m pytest tests/
```

## ✅ Ready to Use

This is a clean, simple, professional structure without over-engineering.
