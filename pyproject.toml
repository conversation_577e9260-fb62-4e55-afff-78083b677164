[build-system]
requires = [
    "setuptools>=45",
    "wheel",
]
build-backend = "setuptools.build_meta"

[project]
name = "aretomo3-gui-professional"
version = "3.0.0"
description = "Enterprise-grade GUI for AreTomo3 tomographic reconstruction with professional structure"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AreTomo3 GUI Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AreTomo3 GUI Development Team", email = "<EMAIL>"}
]
keywords = ["cryo-em", "tomography", "reconstruction", "gui", "microscopy", "aretomo3", "enterprise", "professional"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Environment :: X11 Applications :: Qt",
    "Environment :: Web Environment",
]
requires-python = ">=3.8"
dependencies = [
    # Core GUI Framework
    "PyQt6>=6.4.0",
    "PyQt6-WebEngine>=6.4.0",
    "qtpy>=2.0.0",

    # Scientific Computing Core
    "numpy>=1.21.0",
    "scipy>=1.7.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "h5py>=3.6.0",

    # Visualization and Plotting
    "matplotlib>=3.5.0",
    "plotly>=5.0.0",
    "bokeh>=2.4.0",
    "dash>=2.0.0",
    "seaborn>=0.11.0",
    "pyqtgraph>=0.13.0",

    # File Format Support
    "mrcfile>=1.4.0",
    "tifffile>=2021.11.2",
    "imageio>=2.19.0",
    "Pillow>=8.3.0",

    # 3D Visualization
    "napari[pyqt6]>=0.4.18",
    "vispy>=0.12.0",

    # Web Framework and API
    "fastapi>=0.95.0",
    "uvicorn[standard]>=0.20.0",
    "Flask>=2.0.0",
    "PyJWT>=2.0.0",
    "python-multipart>=0.0.5",
    "websockets>=10.4",
    "pydantic>=1.10.0",

    # System and Monitoring
    "psutil>=5.8.0",
    "watchdog>=2.1.0",
    "PyYAML>=6.0",

    # Additional Utilities
    "typing-extensions>=4.0.0",
    "qrcode[pil]>=7.4.0",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=6.0",
    "pytest-qt>=4.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.900",
    "pre-commit>=2.15.0",
]
# Documentation dependencies
docs = [
    "sphinx>=4.0",
    "sphinx-rtd-theme>=1.0",
    "myst-parser>=0.15",
    "sphinx-autodoc-typehints>=1.12",
]
# Testing dependencies
test = [
    "pytest>=6.0",
    "pytest-qt>=4.0",
    "pytest-cov>=3.0",
    "pytest-mock>=3.6",
]

[project.urls]
Homepage = "https://github.com/aretomo3-gui/aretomo3-gui"
Documentation = "https://aretomo3-gui.readthedocs.io/"
Repository = "https://github.com/aretomo3-gui/aretomo3-gui.git"
"Bug Tracker" = "https://github.com/aretomo3-gui/aretomo3-gui/issues"
Changelog = "https://github.com/aretomo3-gui/aretomo3-gui/blob/main/CHANGELOG.md"

[project.scripts]
aretomo3-gui = "aretomo3_gui.main:main"
aretomo3-gui-web = "aretomo3_gui.web.server:main"
aretomo3-gui-cli = "aretomo3_gui.cli:main"
aretomo3-gui-professional = "aretomo3_gui.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["aretomo3_gui*"]

[tool.setuptools.package-data]
aretomo3_gui = [
    "resources/*",
    "docs/*",
    "*.md",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["aretomo3_gui"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
    "aretomo3_gui",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gui: marks tests that require GUI components",
    "asyncio: marks tests that use asyncio",
]

[tool.coverage.run]
source = ["aretomo3_gui"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
