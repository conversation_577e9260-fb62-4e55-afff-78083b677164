#!/usr/bin/env python3
"""
Simple AreTomo3 GUI Installer
============================
"""

import subprocess
import sys
from pathlib import Path

def install():
    """Simple installation."""
    print("🚀 Installing AreTomo3 GUI...")
    
    # Install dependencies
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    # Install package
    subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."])
    
    print("✅ Installation complete!")
    print("🚀 Run: python -m aretomo3_gui")

if __name__ == "__main__":
    install()
